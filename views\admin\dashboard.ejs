<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
    <style>
        body {
            background: #f5f5f5;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background: #1e4d72;
            min-height: 100vh;
            color: white;
            width: 250px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.9);
            padding: 12px 20px;
            border: none;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0;
        }
        .sidebar .nav-link i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            margin-left: 250px;
            padding: 0;
        }
        .top-header {
            background: #1e4d72;
            color: white;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .content-area {
            padding: 30px;
            background: #f5f5f5;
            min-height: calc(100vh - 70px);
        }
        .stat-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
        .bg-primary-gradient { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .bg-success-gradient { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
        .bg-warning-gradient { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .bg-info-gradient { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        
        .table-hover tbody tr:hover {
            background-color: rgba(102, 126, 234, 0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="p-3 border-bottom">
            <div class="d-flex align-items-center">
                <button class="btn btn-link text-white p-0 me-3" style="font-size: 18px;">
                    <i class="fas fa-bars"></i>
                </button>
                <h5 class="mb-0">Admin Panel</h5>
            </div>
        </div>

        <nav class="nav flex-column mt-3">
            <a class="nav-link active" href="/admin/dashboard">
                <i class="fas fa-tachometer-alt"></i>Dashboard
            </a>
            <a class="nav-link" href="/admin/upload">
                <i class="fas fa-upload"></i>Upload Excel
            </a>
            <a class="nav-link" href="/admin/winners">
                <i class="fas fa-trophy"></i>Winner List
            </a>
            <a class="nav-link" href="/admin/bank-details">
                <i class="fas fa-university"></i>Bank Details
            </a>
            <a class="nav-link" href="/admin/bank-update">
                <i class="fas fa-edit"></i>Bank Update
            </a>
            <a class="nav-link" href="/admin/update-prize">
                <i class="fas fa-gift"></i>Update Prize
            </a>
            <a class="nav-link" href="/admin/prize-request">
                <i class="fas fa-hand-paper"></i>Prize Request
            </a>
            <a class="nav-link" href="/admin/car-processing">
                <i class="fas fa-car"></i>Car Processing Request
            </a>
            <a class="nav-link" href="/admin/logout">
                <i class="fas fa-sign-out-alt"></i>Logout
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Header -->
        <div class="top-header">
            <h4 class="mb-0">Admin Dashboard</h4>
            <span>Welcome, <%= adminUsername %></span>
        </div>

        <!-- Content Area -->
        <div class="content-area">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-chart-line me-2"></i>Dashboard</h2>
                        <div class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            <%= new Date().toLocaleDateString() %>
                        </div>
                    </div>
                    
                    <!-- Alerts -->
                    <% if (success && success.length > 0) { %>
                        <div class="alert alert-success alert-dismissible fade show">
                            <i class="fas fa-check-circle me-2"></i><%= success %>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <% } %>
                    
                    <% if (error && error.length > 0) { %>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <i class="fas fa-exclamation-triangle me-2"></i><%= error %>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <% } %>
                    
                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="card-body d-flex align-items-center">
                                    <div class="stat-icon bg-primary-gradient me-3">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div>
                                        <h5 class="card-title mb-0"><%= stats.totalWinners %></h5>
                                        <small class="text-muted">Total Winners</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="card-body d-flex align-items-center">
                                    <div class="stat-icon bg-warning-gradient me-3">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div>
                                        <h5 class="card-title mb-0"><%= stats.pendingWinners %></h5>
                                        <small class="text-muted">Pending</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="card-body d-flex align-items-center">
                                    <div class="stat-icon bg-success-gradient me-3">
                                        <i class="fas fa-check"></i>
                                    </div>
                                    <div>
                                        <h5 class="card-title mb-0"><%= stats.approvedWinners %></h5>
                                        <small class="text-muted">Approved</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="card-body d-flex align-items-center">
                                    <div class="stat-icon bg-info-gradient me-3">
                                        <i class="fas fa-money-bill"></i>
                                    </div>
                                    <div>
                                        <h5 class="card-title mb-0"><%= stats.paidWinners %></h5>
                                        <small class="text-muted">Paid</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Winners -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>Recent Winners</h5>
                                    <a href="/admin/winners" class="btn btn-sm btn-outline-primary">View All</a>
                                </div>
                                <div class="card-body">
                                    <% if (recentWinners && recentWinners.length > 0) { %>
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>Name</th>
                                                        <th>Phone</th>
                                                        <th>Prize</th>
                                                        <th>Status</th>
                                                        <th>Date</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <% recentWinners.forEach(winner => { %>
                                                        <tr>
                                                            <td><%= winner.name %></td>
                                                            <td><%= winner.phone.replace(/(\d{2})\d+(\d{2})/, '$1XXXXXX$2') %></td>
                                                            <td>₹<%= winner.prizeAmount %></td>
                                                            <td>
                                                                <span class="badge bg-<%= winner.status === 'Approved' ? 'success' : winner.status === 'Pending' ? 'warning' : winner.status === 'Paid' ? 'info' : 'secondary' %>">
                                                                    <%= winner.status %>
                                                                </span>
                                                            </td>
                                                            <td><%= new Date(winner.createdAt).toLocaleDateString() %></td>
                                                        </tr>
                                                    <% }); %>
                                                </tbody>
                                            </table>
                                        </div>
                                    <% } else { %>
                                        <div class="text-center py-4">
                                            <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">No winners found</p>
                                            <a href="/admin/upload" class="btn btn-primary">Upload Winners</a>
                                        </div>
                                    <% } %>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Status Distribution</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="statusChart" width="300" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script>
        // Status Distribution Chart
        const ctx = document.getElementById('statusChart').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Pending', 'Approved', 'Paid'],
                datasets: [{
                    data: [<%= stats.pendingWinners %>, <%= stats.approvedWinners %>, <%= stats.paidWinners %>],
                    backgroundColor: [
                        '#f093fb',
                        '#56ab2f',
                        '#4facfe'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    </script>
</body>
</html>
