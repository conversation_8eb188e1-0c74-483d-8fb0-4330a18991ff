<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>Search Results - Herbal Lucky Draw</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" href="/images/herbal-fav.png" type="image/x-icon" />
    <link href="/css/style1.css" rel="stylesheet" />
    <link href="/css/custom.css" rel="stylesheet" />
    <link href="/css/responsive1.css" rel="stylesheet" />
    <link href="/plugins/sweetalert2/sweetalert2.min.css" rel="stylesheet" />
    <link href="/plugins/floating-wpp/floating-wpp.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <style>
        .search-container {
            background: #f8f9fa;
            padding: 2rem 0;
        }
        .search-form {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .search-results {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .result-item {
            padding: 1.5rem;
            border-bottom: 1px solid #eee;
            transition: background 0.3s ease;
        }
        .result-item:hover {
            background: #f8f9fa;
        }
        .result-item:last-child {
            border-bottom: none;
        }
        .winner-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .winner-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
        }
        .winner-details h5 {
            margin: 0 0 0.5rem 0;
            color: #333;
        }
        .winner-details p {
            margin: 0.25rem 0;
            color: #666;
            font-size: 0.9rem;
        }
        .winner-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            display: inline-block;
        }
        .no-results {
            text-align: center;
            padding: 3rem;
            color: #666;
        }
        .no-results i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #ccc;
        }
        .search-stats {
            background: #e3f2fd;
            padding: 1rem;
            border-left: 4px solid #2196f3;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- main header area -->
    <header class="main-header">
        <!--header upper start-->
        <div class="header-upper">
            <div class="container">
                <ul class="top-left">
                    <li><i class="fa fa-phone"></i>SUPPORT NUMBER:<a href="tel:+919163571821">+919163571821</a></li>
                    <li><i class="fa fa-whatsapp"></i> What's App Number: <a>+919163571821</a></li>
                </ul>
                <div class="top-right"></div>
            </div>
        </div>
        <!--header upper end-->

        <!-- header lower start-->
        <div class="header-lower">
            <div class="container">
                <div class="row">
                    <div class="col-md-3 col-sm-12 col-xs-12">
                        <div class="logo-data-1">
                            <a href="/">
                                <img style="width:90px;" src="/images/herbal-logo.png" />
                            </a>
                        </div>
                    </div>
                    <div class="col-md-9 col-sm-12 col-xs-12">
                        <div class="menu-bar">
                            <nav class="main-menu">
                                <div class="navbar-header">
                                    <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                                        <span class="icon-bar"></span>
                                        <span class="icon-bar"></span>
                                        <span class="icon-bar"></span>
                                    </button>
                                </div>
                                <div class="navbar-collapse collapse clearfix">
                                    <ul class="navigation clearfix">
                                        <li><a href="/Products">Products</a></li>
                                        <li><a href="/Prize">Prize</a></li>
                                        <li><a href="/How-to-Win">How to Win</a></li>
                                        <li><a href="/Winner-List">Winner List</a></li>
                                        <li><a href="/Status">Check Status</a></li>
                                        <li><a href="/Terms">Terms & Conditions</a></li>
                                        <li><a href="/Contact">Contact</a></li>
                                    </ul>
                                </div>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!-- end main header area -->

    <!-- page title start -->
    <div class="about-bg centered">
        <div class="container">
            <div class="text">Search Winners</div>
        </div>
    </div>
    <!--page title end -->

    <!-- Search Container -->
    <div class="search-container">
        <div class="container">
            <!-- Search Form -->
            <div class="search-form">
                <h4><i class="fa fa-search me-2"></i>Search Winners</h4>
                <p class="text-muted mb-3">Search by name, phone number, or winner code</p>
                
                <form id="searchForm">
                    <div class="row">
                        <div class="col-md-6">
                            <input type="text" id="searchQuery" class="form-control" placeholder="Enter name, phone, or W-Code" required>
                        </div>
                        <div class="col-md-3">
                            <select id="searchType" class="form-control">
                                <option value="">All Fields</option>
                                <option value="name">Name</option>
                                <option value="phone">Phone</option>
                                <option value="wcode">W-Code</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary btn-block">
                                <i class="fa fa-search"></i> Search
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Search Results -->
            <div id="searchResults" class="search-results" style="display: none;">
                <div id="searchStats" class="search-stats"></div>
                <div id="resultsContainer"></div>
            </div>

            <!-- Loading -->
            <div id="loadingIndicator" style="display: none; text-align: center; padding: 2rem;">
                <i class="fa fa-spinner fa-spin fa-2x"></i>
                <p>Searching...</p>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="main-footer footer-data-2">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="footer-data-1">
                        <a href="https://wa.me/+919163571821">
                            <p>Head office : <i class='fa fa-whatsapp' aria-hidden='true'></i>+919163571821</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script type="text/javascript" src="/js/jquery-2.1.4.js"></script>
    <script src="/js/bootstrap.min.js"></script>
    <script src="/plugins/sweetalert2/sweetalert2.min.js"></script>
    
    <script>
        $(document).ready(function() {
            $('#searchForm').on('submit', function(e) {
                e.preventDefault();
                performSearch();
            });

            // Auto-search on input (with debounce)
            let searchTimeout;
            $('#searchQuery').on('input', function() {
                clearTimeout(searchTimeout);
                const query = $(this).val().trim();
                if (query.length >= 2) {
                    searchTimeout = setTimeout(performSearch, 500);
                } else if (query.length === 0) {
                    $('#searchResults').hide();
                }
            });
        });

        function performSearch() {
            const query = $('#searchQuery').val().trim();
            const type = $('#searchType').val();

            if (query.length < 2) {
                Swal.fire('Invalid Search', 'Please enter at least 2 characters', 'warning');
                return;
            }

            $('#loadingIndicator').show();
            $('#searchResults').hide();

            $.ajax({
                url: '/search-winners',
                method: 'GET',
                data: { q: query, type: type },
                success: function(response) {
                    $('#loadingIndicator').hide();
                    displayResults(response);
                },
                error: function() {
                    $('#loadingIndicator').hide();
                    Swal.fire('Error', 'Failed to search winners', 'error');
                }
            });
        }

        function displayResults(response) {
            const statsDiv = $('#searchStats');
            const resultsDiv = $('#resultsContainer');
            
            if (response.success && response.winners.length > 0) {
                // Show stats
                statsDiv.html(`
                    <i class="fa fa-info-circle"></i>
                    Found <strong>${response.count}</strong> winner(s) matching "<strong>${response.query}</strong>"
                `);

                // Show results
                let html = '';
                response.winners.forEach(function(winner) {
                    const initials = winner.name.split(' ').map(n => n[0]).join('').toUpperCase();
                    html += `
                        <div class="result-item">
                            <div class="winner-info">
                                <div class="winner-avatar">${initials}</div>
                                <div class="winner-details">
                                    <h5>${winner.name}</h5>
                                    <p><i class="fa fa-phone"></i> ${winner.phone}</p>
                                    <p><i class="fa fa-gift"></i> Prize: ₹${winner.prizeAmount} - ${winner.product}</p>
                                    <p><i class="fa fa-calendar"></i> ${new Date(winner.createdAt).toLocaleDateString()}</p>
                                    <span class="winner-badge">W-Code: ${winner.wcode}</span>
                                </div>
                            </div>
                        </div>
                    `;
                });
                resultsDiv.html(html);
            } else {
                statsDiv.html(`
                    <i class="fa fa-exclamation-triangle"></i>
                    No winners found matching "<strong>${response.query || $('#searchQuery').val()}</strong>"
                `);
                resultsDiv.html(`
                    <div class="no-results">
                        <i class="fa fa-search"></i>
                        <h4>No Results Found</h4>
                        <p>Try searching with different keywords or check the spelling.</p>
                    </div>
                `);
            }

            $('#searchResults').show();
        }
    </script>
</body>
</html>
