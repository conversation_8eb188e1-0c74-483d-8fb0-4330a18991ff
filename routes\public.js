const express = require('express');
const router = express.Router();
const Winner = require('../models/Winner');
const path = require('path');

// GET / - Home page
router.get('/', async (req, res) => {
  try {
    const recentWinners = await Winner.find({ isActive: true })
      .sort({ createdAt: -1 })
      .limit(10);

    res.render('public/index', {
      title: 'Lucky Draw - Win Amazing Prizes',
      recentWinners,
      currentPage: 'home',
      products: [] // Add products if you have a products model
    });
  } catch (error) {
    console.error('Home page error:', error);
    res.render('public/index', {
      title: 'Lucky Draw - Win Amazing Prizes',
      recentWinners: [],
      currentPage: 'home',
      products: []
    });
  }
});

// GET /Winner-List - Dynamic winner list page
router.get('/Winner-List', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = 20;
    const search = req.query.search || '';

    const skip = (page - 1) * limit;

    // Build search query
    let query = { isActive: true, status: { $in: ['Approved', 'Paid'] } };

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } },
        { wcode: { $regex: search, $options: 'i' } }
      ];
    }

    const winners = await Winner.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Winner.countDocuments(query);
    const totalPages = Math.ceil(total / limit);

    // Calculate stats
    const stats = {
      totalWinners: await Winner.countDocuments({ isActive: true, status: { $in: ['Approved', 'Paid'] } }),
      totalPrizeAmount: await Winner.aggregate([
        { $match: { isActive: true, status: { $in: ['Approved', 'Paid'] } } },
        { $group: { _id: null, total: { $sum: '$prizeAmount' } } }
      ]).then(result => result[0]?.total || 0),
      activeWinners: await Winner.countDocuments({ isActive: true, status: 'Approved' }),
      paidWinners: await Winner.countDocuments({ isActive: true, status: 'Paid' })
    };

    res.render('public/winner-list', {
      title: 'Winner List - Lucky Draw',
      winners,
      pagination: {
        page,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
        nextPage: page + 1,
        prevPage: page - 1
      },
      search,
      stats,
      totalWinners: stats.totalWinners,
      currentPage: 'winners'
    });
  } catch (error) {
    console.error('Winner list error:', error);
    res.render('public/winner-list', {
      title: 'Winner List - Lucky Draw',
      winners: [],
      pagination: { page: 1, totalPages: 1, hasNext: false, hasPrev: false },
      search: '',
      stats: { totalWinners: 0, totalPrizeAmount: 0, activeWinners: 0, paidWinners: 0 },
      totalWinners: 0,
      currentPage: 'winners'
    });
  }
});

// GET /Products - Products page
router.get('/Products', (req, res) => {
  res.render('public/products', {
    title: 'Products - Lucky Draw',
    currentPage: 'products'
  });
});

// GET /Prize - Prize page
router.get('/Prize', (req, res) => {
  res.render('public/prize', {
    title: 'Prizes - Lucky Draw',
    currentPage: 'prize'
  });
});

// GET /How-to-Win - How to Win page
router.get('/How-to-Win', (req, res) => {
  res.render('public/how-to-win', {
    title: 'How to Win - Lucky Draw',
    currentPage: 'how-to-win'
  });
});

// GET /Status - Status check page
router.get('/Status', (req, res) => {
  res.render('public/status', {
    title: 'Check Status - Lucky Draw',
    currentPage: 'status'
  });
});

// POST /Status - Handle status check
router.post('/Status', async (req, res) => {
  try {
    const { phone, wcode } = req.body;

    if (!phone || !wcode) {
      return res.render('public/status', {
        title: 'Check Status - Lucky Draw',
        currentPage: 'status',
        error: 'Please enter both phone number and W-Code'
      });
    }

    const winner = await Winner.findOne({
      phone: phone,
      wcode: wcode,
      isActive: true
    });

    if (!winner) {
      return res.render('public/status', {
        title: 'Check Status - Lucky Draw',
        currentPage: 'status',
        error: 'No winner found with the provided details'
      });
    }

    res.render('public/status', {
      title: 'Check Status - Lucky Draw',
      currentPage: 'status',
      winner: winner,
      success: 'Winner details found successfully!'
    });

  } catch (error) {
    console.error('Status check error:', error);
    res.render('public/status', {
      title: 'Check Status - Lucky Draw',
      currentPage: 'status',
      error: 'An error occurred while checking status'
    });
  }
});

// GET /Terms - Terms and Conditions page
router.get('/Terms', (req, res) => {
  res.render('public/terms', {
    title: 'Terms & Conditions - Lucky Draw',
    currentPage: 'terms'
  });
});

// GET /Contact - Contact page
router.get('/Contact', (req, res) => {
  res.render('public/contact', {
    title: 'Contact Us - Lucky Draw',
    currentPage: 'contact'
  });
});

// GET /index-2 - Alternative home page
router.get('/index-2', (req, res) => {
  res.sendFile(path.join(__dirname, '../index-2.html'));
});

// GET /search-result - Dynamic search results page
router.get('/search-result', (req, res) => {
  res.render('public/search-result', {
    title: 'Search Winners - Herbal Lucky Draw'
  });
});

// GET /prize-details - Prize details page
router.get('/prize-details', (req, res) => {
  res.sendFile(path.join(__dirname, '../prize-details.html'));
});

// GET /winner-cash - Winner cash page
router.get('/winner-cash', (req, res) => {
  res.sendFile(path.join(__dirname, '../winner-cash.html'));
});

// GET /car-processing - Car processing page
router.get('/car-processing', (req, res) => {
  res.sendFile(path.join(__dirname, '../car-processing.html'));
});

// POST /check-status - Check winner status by phone or W-Code
router.post('/check-status', async (req, res) => {
  try {
    const { phone, wcode } = req.body;
    
    if (!phone && !wcode) {
      return res.json({ 
        success: false, 
        message: 'Please provide either phone number or W-Code' 
      });
    }
    
    let query = { isActive: true };
    
    if (phone) {
      query.phone = phone;
    } else if (wcode) {
      query.wcode = wcode;
    }
    
    const winner = await Winner.findOne(query);
    
    if (!winner) {
      return res.json({ 
        success: false, 
        message: 'No winner found with the provided information' 
      });
    }
    
    // Return limited information for privacy
    res.json({
      success: true,
      winner: {
        name: winner.name,
        wcode: winner.wcode,
        status: winner.status,
        prizeAmount: winner.prizeAmount,
        product: winner.product,
        date: winner.date
      }
    });
    
  } catch (error) {
    console.error('Status check error:', error);
    res.json({ 
      success: false, 
      message: 'An error occurred while checking status' 
    });
  }
});

// GET /winner-details/:wcode - Get specific winner details
router.get('/winner-details/:wcode', async (req, res) => {
  try {
    const winner = await Winner.findOne({
      wcode: req.params.wcode,
      isActive: true,
      status: 'Approved'
    });

    if (!winner) {
      return res.status(404).json({
        success: false,
        message: 'Winner not found'
      });
    }

    // Return public information only
    res.json({
      success: true,
      winner: {
        name: winner.name,
        wcode: winner.wcode,
        prizeAmount: winner.prizeAmount,
        product: winner.product,
        date: winner.date,
        image: winner.image
      }
    });

  } catch (error) {
    console.error('Winner details error:', error);
    res.status(500).json({
      success: false,
      message: 'An error occurred while fetching winner details'
    });
  }
});

// POST /search-winners - Search winners (for search-result page)
router.post('/search-winners', async (req, res) => {
  try {
    const { query, searchType } = req.body;

    if (!query || query.trim().length < 2) {
      return res.json({
        success: false,
        message: 'Please enter at least 2 characters to search'
      });
    }

    let searchQuery = {
      isActive: true,
      status: 'Approved'
    };

    // Build search based on type
    if (searchType === 'phone') {
      searchQuery.phone = { $regex: query.trim(), $options: 'i' };
    } else if (searchType === 'wcode') {
      searchQuery.wcode = { $regex: query.trim(), $options: 'i' };
    } else if (searchType === 'name') {
      searchQuery.name = { $regex: query.trim(), $options: 'i' };
    } else {
      // Search all fields
      searchQuery.$or = [
        { name: { $regex: query.trim(), $options: 'i' } },
        { phone: { $regex: query.trim(), $options: 'i' } },
        { wcode: { $regex: query.trim(), $options: 'i' } }
      ];
    }

    const winners = await Winner.find(searchQuery)
      .sort({ createdAt: -1 })
      .limit(50)
      .select('name phone wcode prizeAmount product date image createdAt');

    // Mask phone numbers for privacy
    const maskedWinners = winners.map(winner => ({
      ...winner.toObject(),
      phone: winner.phone.replace(/(\d{2})\d+(\d{2})/, '$1XXXXXX$2')
    }));

    res.json({
      success: true,
      winners: maskedWinners,
      count: winners.length,
      query: query.trim()
    });

  } catch (error) {
    console.error('Search winners error:', error);
    res.json({
      success: false,
      message: 'An error occurred while searching winners'
    });
  }
});

// GET /search-winners - GET version for direct URL access
router.get('/search-winners', async (req, res) => {
  try {
    const { q, type } = req.query;

    if (!q || q.trim().length < 2) {
      return res.json({
        success: false,
        message: 'Please enter at least 2 characters to search',
        winners: []
      });
    }

    let searchQuery = {
      isActive: true,
      status: 'Approved'
    };

    // Build search based on type
    if (type === 'phone') {
      searchQuery.phone = { $regex: q.trim(), $options: 'i' };
    } else if (type === 'wcode') {
      searchQuery.wcode = { $regex: q.trim(), $options: 'i' };
    } else if (type === 'name') {
      searchQuery.name = { $regex: q.trim(), $options: 'i' };
    } else {
      // Search all fields
      searchQuery.$or = [
        { name: { $regex: q.trim(), $options: 'i' } },
        { phone: { $regex: q.trim(), $options: 'i' } },
        { wcode: { $regex: q.trim(), $options: 'i' } }
      ];
    }

    const winners = await Winner.find(searchQuery)
      .sort({ createdAt: -1 })
      .limit(50)
      .select('name phone wcode prizeAmount product date image createdAt');

    // Mask phone numbers for privacy
    const maskedWinners = winners.map(winner => ({
      ...winner.toObject(),
      phone: winner.phone.replace(/(\d{2})\d+(\d{2})/, '$1XXXXXX$2')
    }));

    res.json({
      success: true,
      winners: maskedWinners,
      count: winners.length,
      query: q.trim()
    });

  } catch (error) {
    console.error('Search winners error:', error);
    res.json({
      success: false,
      message: 'An error occurred while searching winners',
      winners: []
    });
  }
});

module.exports = router;
