<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prize Request - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f5f5f5;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background: #1e4d72;
            min-height: 100vh;
            color: white;
            width: 250px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.9);
            padding: 12px 20px;
            border: none;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0;
        }
        .sidebar .nav-link i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            margin-left: 250px;
            padding: 0;
        }
        .top-header {
            background: #1e4d72;
            color: white;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .content-area {
            padding: 30px;
            background: #f5f5f5;
            min-height: calc(100vh - 70px);
        }
        .request-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
            margin-top: 20px;
        }
        .request-icon {
            color: #fd7e14;
            margin-right: 10px;
        }
        .status-pending { background-color: #ffc107; }
        .status-approved { background-color: #28a745; }
        .status-rejected { background-color: #dc3545; }
        .status-processing { background-color: #17a2b8; }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="p-3 border-bottom">
            <div class="d-flex align-items-center">
                <button class="btn btn-link text-white p-0 me-3" style="font-size: 18px;">
                    <i class="fas fa-bars"></i>
                </button>
                <h5 class="mb-0">Admin Panel</h5>
            </div>
        </div>
        
        <nav class="nav flex-column mt-3">
            <a class="nav-link" href="/admin/dashboard">
                <i class="fas fa-tachometer-alt"></i>Dashboard
            </a>
            <a class="nav-link" href="/admin/upload">
                <i class="fas fa-upload"></i>Upload Excel
            </a>
            <a class="nav-link" href="/admin/winners">
                <i class="fas fa-trophy"></i>Winner List
            </a>
            <a class="nav-link" href="/admin/bank-details">
                <i class="fas fa-university"></i>Bank Details
            </a>
            <a class="nav-link" href="/admin/bank-update">
                <i class="fas fa-edit"></i>Bank Update
            </a>
            <a class="nav-link" href="/admin/update-prize">
                <i class="fas fa-gift"></i>Update Prize
            </a>
            <a class="nav-link active" href="/admin/prize-request">
                <i class="fas fa-hand-paper"></i>Prize Request
            </a>
            <a class="nav-link" href="/admin/car-processing">
                <i class="fas fa-car"></i>Car Processing Request
            </a>
            <a class="nav-link" href="/admin/logout">
                <i class="fas fa-sign-out-alt"></i>Logout
            </a>
        </nav>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Header -->
        <div class="top-header">
            <h4 class="mb-0">Admin Dashboard</h4>
            <span>Welcome, <%= adminUsername %></span>
        </div>
        
        <!-- Content Area -->
        <div class="content-area">
            <div class="request-card">
                <div class="d-flex align-items-center mb-4">
                    <i class="fas fa-hand-paper request-icon" style="font-size: 24px;"></i>
                    <h3 class="mb-0">Prize Request</h3>
                </div>
                
                <% if (success && success.length > 0) { %>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i><%= success %>
                    </div>
                <% } %>
                
                <% if (error && error.length > 0) { %>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i><%= error %>
                    </div>
                <% } %>
                
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Request ID</th>
                                <th>Winner Name</th>
                                <th>Phone</th>
                                <th>Prize Amount</th>
                                <th>Request Date</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% if (prizeRequests && prizeRequests.length > 0) { %>
                                <% prizeRequests.forEach((request, index) => { %>
                                    <tr>
                                        <td>#<%= request.id || (1000 + index) %></td>
                                        <td><%= request.winnerName %></td>
                                        <td><%= request.phone %></td>
                                        <td>₹<%= request.prizeAmount %></td>
                                        <td><%= new Date(request.requestDate || Date.now()).toLocaleDateString() %></td>
                                        <td>
                                            <span class="badge status-<%= request.status.toLowerCase() %>">
                                                <%= request.status %>
                                            </span>
                                        </td>
                                        <td>
                                            <% if (request.status === 'Pending') { %>
                                                <button class="btn btn-sm btn-success me-1" onclick="updateStatus('<%= request.id %>', 'Approved')">
                                                    <i class="fas fa-check"></i> Approve
                                                </button>
                                                <button class="btn btn-sm btn-danger" onclick="updateStatus('<%= request.id %>', 'Rejected')">
                                                    <i class="fas fa-times"></i> Reject
                                                </button>
                                            <% } else if (request.status === 'Approved') { %>
                                                <button class="btn btn-sm btn-info" onclick="updateStatus('<%= request.id %>', 'Processing')">
                                                    <i class="fas fa-cog"></i> Process
                                                </button>
                                            <% } else { %>
                                                <button class="btn btn-sm btn-secondary" disabled>
                                                    <i class="fas fa-check-circle"></i> Completed
                                                </button>
                                            <% } %>
                                        </td>
                                    </tr>
                                <% }) %>
                            <% } else { %>
                                <tr>
                                    <td colspan="7" class="text-center">No prize requests found</td>
                                </tr>
                            <% } %>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function updateStatus(requestId, newStatus) {
            if (confirm(`Are you sure you want to ${newStatus.toLowerCase()} this request?`)) {
                fetch('/admin/prize-request/update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        requestId: requestId,
                        status: newStatus
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error updating status: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error updating status');
                });
            }
        }
    </script>
</body>
</html>
