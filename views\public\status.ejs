<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title><%= title || 'Check Status - Lucky Draw' %></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" href="/images/herbal-fav.png" type="image/x-icon" />
    <link href="/css/style1.css" rel="stylesheet" />
    <link href="/css/custom.css" rel="stylesheet" />
    <link href="/css/responsive1.css" rel="stylesheet" />
    <link href="/plugins/sweetalert2/sweetalert2.min.css" rel="stylesheet" />
    <style>
        .status-form {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 30px 0;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-control {
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-check {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 15px 40px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 16px;
        }
        .btn-check:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .winner-details {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 30px;
            margin: 20px 0;
        }
        .winner-details h3 {
            color: #155724;
            margin-bottom: 20px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #c3e6cb;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: 600;
            color: #155724;
        }
        .detail-value {
            color: #155724;
        }
        .prize-highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
        }
        .prize-amount {
            font-size: 36px;
            font-weight: bold;
            color: #856404;
        }
        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
        }
        .status-approved {
            background: #d4edda;
            color: #155724;
        }
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        .status-paid {
            background: #d1ecf1;
            color: #0c5460;
        }
        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <%- include('partials/header') %>

    <!-- Page Title -->
    <section class="page-title">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <h1>Check Winner Status</h1>
                    <p>Enter your details to check if you're a lucky winner</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Status Check Section -->
    <section class="status-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="status-form">
                        <h2 class="text-center mb-4">Winner Status Check</h2>
                        
                        <% if (typeof error !== 'undefined' && error) { %>
                            <div class="alert alert-danger">
                                <i class="fa fa-exclamation-triangle"></i> <%= error %>
                            </div>
                        <% } %>
                        
                        <% if (typeof success !== 'undefined' && success) { %>
                            <div class="alert alert-success">
                                <i class="fa fa-check-circle"></i> <%= success %>
                            </div>
                        <% } %>
                        
                        <form method="POST" action="/Status">
                            <div class="form-group">
                                <label for="phone">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       placeholder="Enter your phone number" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="wcode">W-Code</label>
                                <input type="text" class="form-control" id="wcode" name="wcode" 
                                       placeholder="Enter your W-Code" required>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-check">
                                    <i class="fa fa-search"></i> Check Status
                                </button>
                            </div>
                        </form>
                        
                        <% if (typeof winner !== 'undefined' && winner) { %>
                            <div class="winner-details">
                                <h3><i class="fa fa-trophy"></i> Congratulations! You're a Winner!</h3>
                                
                                <div class="prize-highlight">
                                    <div class="prize-amount">₹<%= winner.prizeAmount %></div>
                                    <p>Prize Amount</p>
                                </div>
                                
                                <div class="detail-row">
                                    <span class="detail-label">Name:</span>
                                    <span class="detail-value"><%= winner.name %></span>
                                </div>
                                
                                <div class="detail-row">
                                    <span class="detail-label">Phone:</span>
                                    <span class="detail-value"><%= winner.phone %></span>
                                </div>
                                
                                <div class="detail-row">
                                    <span class="detail-label">W-Code:</span>
                                    <span class="detail-value"><%= winner.wcode %></span>
                                </div>
                                
                                <div class="detail-row">
                                    <span class="detail-label">Product:</span>
                                    <span class="detail-value"><%= winner.product || 'Hammer of Thor Original' %></span>
                                </div>
                                
                                <div class="detail-row">
                                    <span class="detail-label">Win Date:</span>
                                    <span class="detail-value"><%= new Date(winner.createdAt).toLocaleDateString() %></span>
                                </div>
                                
                                <div class="detail-row">
                                    <span class="detail-label">Status:</span>
                                    <span class="detail-value">
                                        <span class="status-badge status-<%= winner.status.toLowerCase() %>">
                                            <%= winner.status %>
                                        </span>
                                    </span>
                                </div>
                                
                                <% if (winner.status === 'Approved') { %>
                                    <div class="alert alert-info mt-3">
                                        <h5><i class="fa fa-info-circle"></i> Next Steps:</h5>
                                        <p>Your prize has been approved! Please contact our support team to claim your prize.</p>
                                        <p><strong>Contact:</strong> +919163571821</p>
                                    </div>
                                <% } else if (winner.status === 'Paid') { %>
                                    <div class="alert alert-success mt-3">
                                        <h5><i class="fa fa-check-circle"></i> Prize Claimed!</h5>
                                        <p>Congratulations! Your prize has been successfully processed and paid.</p>
                                    </div>
                                <% } else if (winner.status === 'Pending') { %>
                                    <div class="alert alert-warning mt-3">
                                        <h5><i class="fa fa-clock-o"></i> Under Review</h5>
                                        <p>Your winning entry is currently under review. We'll contact you soon!</p>
                                    </div>
                                <% } %>
                            </div>
                        <% } %>
                        
                        <div class="instructions">
                            <h4><i class="fa fa-info-circle"></i> Instructions:</h4>
                            <ul>
                                <li>Enter the phone number you used while placing the order</li>
                                <li>Enter the W-Code you received after purchase</li>
                                <li>If you're a winner, your details will be displayed</li>
                                <li>Contact our support team for any assistance</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Support Section -->
    <section class="support-section">
        <div class="container">
            <div class="row">
                <div class="col-md-12 text-center">
                    <h2>Need Help?</h2>
                    <p>Contact our support team for assistance</p>
                    <div class="contact-options">
                        <a href="tel:+919163571821" class="btn btn-primary btn-lg me-3">
                            <i class="fa fa-phone"></i> Call Support
                        </a>
                        <a href="https://wa.me/919163571821" class="btn btn-success btn-lg" target="_blank">
                            <i class="fa fa-whatsapp"></i> WhatsApp
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <%- include('partials/footer') %>

    <!-- Scripts -->
    <script src="/js/jquery-2.1.4.js"></script>
    <script src="/js/bootstrap.min.js"></script>
    <script src="/js/script.js"></script>
    <script src="/plugins/sweetalert2/sweetalert2.min.js"></script>
</body>
</html>
