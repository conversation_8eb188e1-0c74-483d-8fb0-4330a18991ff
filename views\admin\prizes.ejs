<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 2px 0;
            border-radius: 8px;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }
        .main-content {
            background: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
        }
        .prize-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
        }
        .medal {
            font-size: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar">
                    <div class="p-4">
                        <h4 class="text-white mb-4">
                            <i class="fas fa-shield-alt me-2"></i>Admin Panel
                        </h4>
                        <nav class="nav flex-column">
                            <a class="nav-link" href="/admin/dashboard">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                            <a class="nav-link" href="/admin/winners">
                                <i class="fas fa-trophy me-2"></i>Winners
                            </a>
                            <a class="nav-link" href="/admin/upload">
                                <i class="fas fa-upload me-2"></i>Upload Excel
                            </a>
                            <a class="nav-link active" href="/admin/prizes">
                                <i class="fas fa-gift me-2"></i>Prize Management
                            </a>
                            <a class="nav-link" href="/admin/company-bank">
                                <i class="fas fa-building me-2"></i>Company Bank
                            </a>
                            <a class="nav-link" href="/admin/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="main-content p-4">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-gift me-2"></i>Prize Management</h2>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPrizeModal">
                            <i class="fas fa-plus me-2"></i>Add New Prize
                        </button>
                    </div>

                    <!-- Success/Error Messages -->
                    <% if (success && success.length > 0) { %>
                        <div class="alert alert-success alert-dismissible fade show">
                            <%= success %>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <% } %>
                    <% if (error && error.length > 0) { %>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <%= error %>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <% } %>

                    <!-- Prizes Table -->
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Position</th>
                                            <th>Medal</th>
                                            <th>Image</th>
                                            <th>Title</th>
                                            <th>Description</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% if (prizes && prizes.length > 0) { %>
                                            <% prizes.forEach(prize => { %>
                                                <tr>
                                                    <td><span class="badge bg-primary"><%= prize.position %></span></td>
                                                    <td><span class="medal"><%= prize.medal %></span></td>
                                                    <td>
                                                        <img src="/images/<%= prize.image %>" alt="<%= prize.title %>" class="prize-image">
                                                    </td>
                                                    <td><strong><%= prize.title %></strong></td>
                                                    <td><%= prize.description %></td>
                                                    <td><strong>₹<%= prize.amount ? prize.amount.toLocaleString('en-IN') : '0' %></strong></td>
                                                    <td>
                                                        <% if (prize.isActive) { %>
                                                            <span class="badge bg-success">Active</span>
                                                        <% } else { %>
                                                            <span class="badge bg-secondary">Inactive</span>
                                                        <% } %>
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary me-1" onclick="editPrize('<%= prize._id %>')">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-danger" onclick="deletePrize('<%= prize._id %>')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            <% }) %>
                                        <% } else { %>
                                            <tr>
                                                <td colspan="8" class="text-center py-4">
                                                    <i class="fas fa-gift fa-3x text-muted mb-3"></i>
                                                    <p class="text-muted">No prizes found. Add your first prize!</p>
                                                </td>
                                            </tr>
                                        <% } %>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Prize Modal -->
    <div class="modal fade" id="addPrizeModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Add New Prize</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="/admin/prizes/add" method="POST">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Position *</label>
                                    <input type="number" class="form-control" name="position" required min="1">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Medal</label>
                                    <select class="form-control" name="medal">
                                        <option value="🥇">🥇 Gold</option>
                                        <option value="🥈">🥈 Silver</option>
                                        <option value="🥉">🥉 Bronze</option>
                                        <option value="🏆">🏆 Trophy</option>
                                        <option value="🎁">🎁 Gift</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Title *</label>
                            <input type="text" class="form-control" name="title" required placeholder="e.g., Maruti XL6">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description *</label>
                            <input type="text" class="form-control" name="description" required placeholder="e.g., Maruti XL6 Rs 14,80,000">
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Amount *</label>
                                    <input type="number" class="form-control" name="amount" required placeholder="1480000">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Image Filename *</label>
                                    <input type="text" class="form-control" name="image" required placeholder="win1.jpg">
                                    <small class="text-muted">Upload image to /images/ folder first</small>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Emoji</label>
                            <input type="text" class="form-control" name="emoji" placeholder="🏆" maxlength="2">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Prize</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function deletePrize(id) {
            if (confirm('Are you sure you want to delete this prize?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/admin/prizes/delete/${id}`;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function editPrize(id) {
            // You can implement edit functionality here
            alert('Edit functionality - redirect to edit page or open edit modal');
        }
    </script>
</body>
</html>
