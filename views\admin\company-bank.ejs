<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
        }
        .main-content {
            background: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .table th {
            background: #f8f9fa;
            border: none;
            font-weight: 600;
        }
        .badge-primary { background: #007bff !important; }
        .badge-success { background: #28a745 !important; }
        .badge-warning { background: #ffc107 !important; color: #212529 !important; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-3">
                    <h4 class="text-white mb-4">
                        <i class="fas fa-crown me-2"></i>Admin Panel
                    </h4>
                    <nav class="nav flex-column">
                        <a class="nav-link" href="/admin/dashboard">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link" href="/admin/winners">
                            <i class="fas fa-trophy me-2"></i>Winners
                        </a>
                        <a class="nav-link" href="/admin/upload">
                            <i class="fas fa-upload me-2"></i>Upload
                        </a>
                        <!-- <a class="nav-link" href="/admin/bank-details">
                            <i class="fas fa-university me-2"></i>Bank Details
                        </a> -->
                        <a class="nav-link" href="/admin/company-bank">
                            <i class="fas fa-building"></i>Company Bank
                        </a>
                        <a class="nav-link" href="/admin/prize-request">
                            <i class="fas fa-gift me-2"></i>Prize Requests
                        </a>
                        <a class="nav-link" href="/admin/logout">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content p-4">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-building me-2"></i><%= title %></h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCompanyBankModal">
                        <i class="fas fa-plus me-2"></i>Add Company Bank Account
                    </button>
                </div>

                <!-- Alerts -->
                <% if (typeof success !== 'undefined' && success && success.length > 0) { %>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <%= success %>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <% } %>
                <% if (typeof error !== 'undefined' && error && error.length > 0) { %>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <%= error %>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <% } %>

                <!-- Company Bank Details Table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Company Name</th>
                                        <th>Bank Name</th>
                                        <th>Account Number</th>
                                        <th>IFSC Code</th>
                                        <th>Account Holder</th>
                                        <th>Purpose</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% if (companyBankDetails && companyBankDetails.length > 0) { %>
                                        <% companyBankDetails.forEach(bank => { %>
                                            <tr>
                                                <td><%= bank.companyName %></td>
                                                <td><%= bank.bankName %></td>
                                                <td>
                                                    <span class="font-monospace">
                                                        <%= bank.accountNumber.slice(0, 4) %>****<%= bank.accountNumber.slice(-4) %>
                                                    </span>
                                                </td>
                                                <td><%= bank.ifscCode %></td>
                                                <td><%= bank.accountHolderName %></td>
                                                <td>
                                                    <span class="badge bg-info"><%= bank.purpose %></span>
                                                </td>
                                                <td>
                                                    <% if (bank.isPrimary) { %>
                                                        <span class="badge bg-success">Primary</span>
                                                    <% } else { %>
                                                        <span class="badge bg-secondary">Active</span>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button class="btn btn-sm btn-outline-primary" onclick="viewBankDetails('<%= bank._id %>')">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteCompanyBank('<%= bank._id %>')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <% }) %>
                                    <% } else { %>
                                        <tr>
                                            <td colspan="8" class="text-center py-4">
                                                <i class="fas fa-building fa-3x text-muted mb-3"></i>
                                                <p class="text-muted">No company bank details found</p>
                                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCompanyBankModal">
                                                    <i class="fas fa-plus me-2"></i>Add First Bank Account
                                                </button>
                                            </td>
                                        </tr>
                                    <% } %>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Company Bank Modal -->
    <div class="modal fade" id="addCompanyBankModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add Company Bank Account</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="/admin/company-bank" method="POST">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="companyName" class="form-label">Company Name</label>
                                    <input type="text" class="form-control" id="companyName" name="companyName" value="Herbal Ayurveda Pvt. Ltd." required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="displayName" class="form-label">Display Name</label>
                                    <input type="text" class="form-control" id="displayName" name="displayName" value="Prize Distributor Department">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="bankName" class="form-label">Bank Name *</label>
                                    <input type="text" class="form-control" id="bankName" name="bankName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="branchName" class="form-label">Branch Name</label>
                                    <input type="text" class="form-control" id="branchName" name="branchName">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="accountNumber" class="form-label">Account Number *</label>
                                    <input type="text" class="form-control" id="accountNumber" name="accountNumber" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="ifscCode" class="form-label">IFSC Code *</label>
                                    <input type="text" class="form-control" id="ifscCode" name="ifscCode" style="text-transform: uppercase;" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="accountHolderName" class="form-label">Account Holder Name *</label>
                            <input type="text" class="form-control" id="accountHolderName" name="accountHolderName" required>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="accountType" class="form-label">Account Type</label>
                                    <select class="form-select" id="accountType" name="accountType">
                                        <option value="Current">Current</option>
                                        <option value="Savings">Savings</option>
                                        <option value="Business">Business</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="purpose" class="form-label">Purpose</label>
                                    <select class="form-select" id="purpose" name="purpose">
                                        <option value="Prize Distribution">Prize Distribution</option>
                                        <option value="Registration Fees">Registration Fees</option>
                                        <option value="General">General</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contactPhone" class="form-label">Contact Phone</label>
                                    <input type="text" class="form-control" id="contactPhone" name="contactPhone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="upiId" class="form-label">UPI ID</label>
                                    <input type="text" class="form-control" id="upiId" name="upiId">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="instructions" class="form-label">Instructions</label>
                            <textarea class="form-control" id="instructions" name="instructions" rows="3" placeholder="Special instructions for users..."></textarea>
                        </div>
                        
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="isPrimary" name="isPrimary">
                            <label class="form-check-label" for="isPrimary">
                                Set as Primary Account
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Bank Account</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewBankDetails(id) {
            // Implement view functionality
            alert('View bank details for ID: ' + id);
        }

        function deleteCompanyBank(id) {
            if (confirm('Are you sure you want to delete this company bank account?')) {
                fetch(`/admin/company-bank/${id}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error deleting bank account: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error deleting bank account');
                });
            }
        }
    </script>
</body>
</html>
