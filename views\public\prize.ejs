



<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">

<!-- Mirrored from herballuckydraw.com/Prize.php by  Website Copier/3.x [XR&CO'2014], Fri, 01 Aug 2025 09:08:12 GMT -->
<!-- Added by  --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by  -->
<meta http-equiv="content-type" content="text/html;charset=utf-8" />
<head><meta charset="utf-8" /><meta http-equiv="X-UA-Compatible" content="IE=edge" /><title>
	Online Shopping  
</title><meta name="viewport" content="width=device-width, initial-scale=1.0" /><link rel="icon" href="images/herbal-fav.png" type="image/x-icon" /><link href="css/style1.css" rel="stylesheet" /><link href="css/custom.css" rel="stylesheet" /><link href="css/responsive1.css" rel="stylesheet" /><link href="plugins/sweetalert2/sweetalert2.min.css" rel="stylesheet" /><link href="plugins/floating-wpp/floating-wpp.css" rel="stylesheet" />
</head>
<body>
 
         <!-- main header area -->
        <header class="main-header">
            <!--header upper start-->
            <div class="header-upper">
                <div class="container">
                    <ul class="top-left">
                        <li>  <i class="fa fa-phone"></i>SUPPORT NUMBER:<a id="SupportLinkButton1" href="tel:+919341098599 ">+919163571821 </a></li>
                        <li>  <i class="fa fa-whatsapp"></i> What’s App Number: <a>+919163571821</a></li>
                    </ul>
                    <div class="top-right">
                     </div>
                </div>
            </div>
            <!--header upper end-->

            <!-- header lower start-->
            <div class="header-lower">
                <div class="container">
                    <div class="row">
                        <div class="col-md-3 col-sm-12 col-xs-12">
                            <div class="logo-data-1">
                                <a href="/">
                                    <img style="width:90px; " src="images/herbal-logo.png" /></a>
                            </div>
                        </div>
                        <div class="col-md-9 col-sm-12 col-xs-12">
                            <div class="menu-bar">
                                <nav class="main-menu">
                                    <div class="navbar-header">
                                        <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                                            <span class="icon-bar"></span>
                                            <span class="icon-bar"></span>
                                            <span class="icon-bar"></span>
                                        </button>
                                    </div>
                                    <div class="navbar-collapse collapse clearfix">
                                        <ul class="navigation clearfix">
                                            <li><a href="/Products" >Products</a></li>
                                            <li><a href="/Prize" >Prize</a></li>
                                            <li><a href="/How-to-Win" >How to Win</a></li>
                                            <li><a href="/Winner-List" >Winner List</a></li>
                                            <li><a href="/Status" >Check Status</a></li>
                                            <li><a href="/Terms" >Terms & Conditions</a></li>
                                            <li><a href="/Contact" >Contact</a></li>
                                        </ul>
                                    </div>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>
        <!-- end main header area -->

   
 
     <!-- page title start -->
    <div class="about-bg centered">
        <div class="container">
            <div class="text">Our Prize</div>
        </div>
    </div>
    <!--page title end -->
    
	 <!-- fact-counter -->
    <section class="prize-list-data-1">
        <div class="container">
			<div class="feature-title centered prize-list-data-4">
                <div class="section-title">
                    <h2>Prizes to win</h2></div>
                <div class="title-text">
                    <p>Here are the list of prizes to be won</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <!-- <div class="prize-list-data-2">
						<img src="images/prize1.jpg" class="img-responsive"/>
					</div> -->
                </div>
				<div class="col-md-12">
                    <style>
                        .responsive-prize-container {
                            background: linear-gradient(135deg, #003f64 0%, #253559 100%);
                            padding: 40px;
                            border-radius: 15px;
                            box-shadow: 0 15px 40px rgba(0,63,100,0.3);
                            margin: 20px 0;
                            border: 3px solid #f6b129;
                        }

                        .responsive-prize-title {
                            color: #f6b129;
                            text-align: center;
                            margin-bottom: 30px;
                            font-size: 32px;
                            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
                            font-family: 'Montserrat', sans-serif;
                            font-weight: 700;
                            text-transform: uppercase;
                        }

                        .responsive-prize-grid {
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                            gap: 20px;
                            justify-content: center;
                        }

                        .responsive-prize-card {
                            background: rgba(255,255,255,0.98);
                            padding: 25px;
                            border-radius: 12px;
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                            transition: all 0.3s ease;
                            border-left: 5px solid;
                        }

                        .responsive-prize-card:hover {
                            transform: translateY(-5px);
                            box-shadow: 0 12px 35px rgba(0,0,0,0.25);
                        }

                        .prize-card-1 { border-left-color: #FFD700; }
                        .prize-card-2 { border-left-color: #C0C0C0; }
                        .prize-card-3 { border-left-color: #CD7F32; }

                        .responsive-prize-text {
                            color: #003f64;
                            font-size: 18px;
                            font-weight: 700;
                            font-family: 'Montserrat', sans-serif;
                            flex: 1;
                        }

                        .responsive-prize-emoji {
                            font-size: 24px;
                            margin-right: 10px;
                        }

                        .responsive-prize-amount {
                            color: #f6b129;
                            font-size: 16px;
                            display: block;
                            margin-top: 5px;
                        }

                        .responsive-prize-image {
                            width: 120px;
                            height: 90px;
                            border-radius: 10px;
                            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                            border: 2px solid #f6b129;
                            margin-left: 15px;
                            flex-shrink: 0;
                        }

                        /* Tablet Styles */
                        @media (max-width: 768px) {
                            .responsive-prize-container {
                                padding: 25px 15px;
                                margin: 15px 0;
                                border-width: 2px;
                            }

                            .responsive-prize-title {
                                font-size: 24px;
                                margin-bottom: 20px;
                            }

                            .responsive-prize-grid {
                                grid-template-columns: 1fr;
                                gap: 15px;
                            }

                            .responsive-prize-card {
                                padding: 20px 15px;
                                flex-direction: column;
                                text-align: center;
                                border-left: none;
                                border-top: 4px solid;
                            }

                            .responsive-prize-text {
                                font-size: 16px;
                                margin-bottom: 15px;
                            }

                            .responsive-prize-emoji {
                                font-size: 20px;
                                margin-right: 8px;
                            }

                            .responsive-prize-amount {
                                font-size: 15px;
                            }

                            .responsive-prize-image {
                                width: 100px;
                                height: 75px;
                                margin-left: 0;
                            }
                        }

                        /* Mobile Styles */
                        @media (max-width: 480px) {
                            .responsive-prize-container {
                                padding: 20px 10px;
                                margin: 10px 0;
                                border-radius: 10px;
                            }

                            .responsive-prize-title {
                                font-size: 20px;
                                margin-bottom: 15px;
                                line-height: 1.2;
                            }

                            .responsive-prize-card {
                                padding: 15px 10px;
                                border-radius: 8px;
                            }

                            .responsive-prize-text {
                                font-size: 14px;
                            }

                            .responsive-prize-emoji {
                                font-size: 18px;
                                margin-right: 5px;
                            }

                            .responsive-prize-amount {
                                font-size: 13px;
                            }

                            .responsive-prize-image {
                                width: 80px;
                                height: 60px;
                                border-width: 1px;
                            }
                        }

                        /* Small Mobile */
                        @media (max-width: 400px) {
                            .responsive-prize-container {
                                padding: 15px 5px;
                                margin: 10px 0;
                                border-width: 2px;
                                border-radius: 8px;
                            }

                            .responsive-prize-title {
                                font-size: 18px;
                                margin-bottom: 12px;
                                line-height: 1.1;
                                padding: 0 5px;
                            }

                            .responsive-prize-grid {
                                gap: 10px;
                            }

                            .responsive-prize-card {
                                padding: 12px 8px;
                                border-radius: 6px;
                                margin: 0 2px;
                            }

                            .responsive-prize-text {
                                font-size: 12px;
                                margin-bottom: 10px;
                                line-height: 1.3;
                            }

                            .responsive-prize-emoji {
                                font-size: 16px;
                                margin-right: 4px;
                            }

                            .responsive-prize-amount {
                                font-size: 11px;
                                margin-top: 3px;
                            }

                            .responsive-prize-image {
                                width: 65px;
                                height: 45px;
                                border-width: 1px;
                                border-radius: 4px;
                            }
                        }

                        /* Extra Small Mobile */
                        @media (max-width: 360px) {
                            .responsive-prize-container {
                                padding: 12px 3px;
                                margin: 8px 0;
                                border-radius: 6px;
                            }

                            .responsive-prize-title {
                                font-size: 16px;
                                margin-bottom: 10px;
                                padding: 0 3px;
                            }

                            .responsive-prize-grid {
                                gap: 8px;
                            }

                            .responsive-prize-card {
                                padding: 10px 6px;
                                border-radius: 5px;
                                margin: 0 1px;
                            }

                            .responsive-prize-text {
                                font-size: 11px;
                                margin-bottom: 8px;
                            }

                            .responsive-prize-emoji {
                                font-size: 14px;
                                margin-right: 3px;
                            }

                            .responsive-prize-amount {
                                font-size: 10px;
                                margin-top: 2px;
                            }

                            .responsive-prize-image {
                                width: 55px;
                                height: 40px;
                                border-radius: 3px;
                            }
                        }

                        /* Ultra Small Mobile */
                        @media (max-width: 320px) {
                            .responsive-prize-container {
                                padding: 10px 2px;
                                margin: 5px 0;
                                border-radius: 5px;
                                border-width: 1px;
                            }

                            .responsive-prize-title {
                                font-size: 14px;
                                margin-bottom: 8px;
                                padding: 0 2px;
                            }

                            .responsive-prize-grid {
                                gap: 6px;
                            }

                            .responsive-prize-card {
                                padding: 8px 4px;
                                border-radius: 4px;
                                margin: 0;
                            }

                            .responsive-prize-text {
                                font-size: 10px;
                                margin-bottom: 6px;
                                line-height: 1.2;
                            }

                            .responsive-prize-emoji {
                                font-size: 12px;
                                margin-right: 2px;
                            }

                            .responsive-prize-amount {
                                font-size: 9px;
                                margin-top: 1px;
                            }

                            .responsive-prize-image {
                                width: 50px;
                                height: 35px;
                                border-radius: 2px;
                            }
                        }
                    </style>

                    <div class="responsive-prize-container">
						<h3 class="responsive-prize-title">🏆 AMAZING PRIZES 🏆</h3>
						<div class="responsive-prize-grid">
							<div class="responsive-prize-card prize-card-1">
								<div class="responsive-prize-text">
                                    <span class="responsive-prize-emoji" style="color: #FFD700;">🥇</span>
                                    <strong>1st Prize:</strong>
                                    <span class="responsive-prize-amount">Maruti XL6 Rs 14,80,000</span>
                                </div>
								<img src="images/win1.jpg" class="responsive-prize-image" alt="Maruti XL6"/>
							</div>
							<div class="responsive-prize-card prize-card-2">
								<div class="responsive-prize-text">
                                    <span class="responsive-prize-emoji" style="color: #C0C0C0;">🥈</span>
                                    <strong>2nd Prize:</strong>
                                    <span class="responsive-prize-amount">Tata Nexon Rs 9,80,000</span>
                                </div>
								<img src="images/win2.jpg" class="responsive-prize-image" alt="Tata Nexon"/>
							</div>
							<div class="responsive-prize-card prize-card-3">
								<div class="responsive-prize-text">
                                    <span class="responsive-prize-emoji" style="color: #CD7F32;">🥉</span>
                                    <strong>3rd Prize:</strong>
                                    <span class="responsive-prize-amount">Maruti Swift Dzire Rs 9,30,000</span>
                                </div>
								<img src="images/win3.jpg" class="responsive-prize-image" alt="Maruti Swift Dzire"/>
							</div>
						</div>
					</div>
                </div>
            </div>
        </div>
    </section>
    <!-- fact-counter section end -->
    

		<footer class="main-footer footer-data-2">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <div class="footer-data-1">
                            <a href="https://wa.me/+919163571821"> <p>Head office : <i class='fa fa-whatsapp' aria-hidden='true'></i>+919163571821</p>
                            </a>
							
                        </div>
                    </div>
                </div>
            </div>
        </footer>
        <!-- main footer area end -->
        <!-- footer bottom -->
        <section class="footer-bottom centered">
            <div class="container">
                <div class="copyright">
                     <p>Herbal Ayurveda Pvt. Ltd.</p>
                </div>
            </div>
        </section>
        <div id="myButton"></div>
        <input name="ctl00$HFSupportNumber" value="+919163571821" id="HFSupportNumber" type="hidden" />
  

    <!--jquery js -->

    <script type="text/javascript" src="js/jquery-2.1.4.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/slider-active.js" type="text/javascript"></script>
    <script src="js/owl.carousel.min.js"></script>
    <script src="js/wow.js"></script>
    <script src="js/validation.js"></script>
    <script src="js/jquery-ui.js"></script>
    <script src="js/bxslider.js"></script>
    <script type="text/javascript" src="js/SmoothScroll.js"></script>
    <script type="text/javascript" src="js/jQuery.style.switcher.min.js"></script>
    <script src="js/jquery.fancybox.pack.js"></script>

    <script src="revolution/js/jquery.themepunch.tools.min.js" type="text/javascript">
    </script>
    <script src="revolution/js/jquery.themepunch.revolution.min.js" type="text/javascript">
    </script>
    <!-- slider revolution 5.0 extensions  (load extensions only on local file systems !  the following part can be removed on server for on demand loading) -->
    <script src="revolution/js/extensions/revolution.extension.actions.min.js" type="text/javascript">
    </script>
    <script src="revolution/js/extensions/revolution.extension.carousel.min.js" type="text/javascript">
    </script>
    <script src="revolution/js/extensions/revolution.extension.kenburn.min.js" type="text/javascript">
    </script>
    <script src="revolution/js/extensions/revolution.extension.layeranimation.min.js" type="text/javascript">
    </script>
    <script src="revolution/js/extensions/revolution.extension.migration.min.js" type="text/javascript">
    </script>
    <script src="revolution/js/extensions/revolution.extension.navigation.min.js" type="text/javascript">
    </script>
    <script src="revolution/js/extensions/revolution.extension.parallax.min.js" type="text/javascript">
    </script>
    <script src="revolution/js/extensions/revolution.extension.slideanims.min.js" type="text/javascript">
    </script>
    <script src="revolution/js/extensions/revolution.extension.video.min.js" type="text/javascript">
    </script>

    <script src="js/script.js"></script>
    <script src="plugins/sweetalert2/sweetalert2.min.js"></script>
    <script src="plugins/floating-wpp/floating-wpp.js"></script>
    <script type="text/javascript">
        $(function () {
            $('#myButton').floatingWhatsApp({
                phone: document.getElementById('HFSupportNumber').value,
                popupMessage: 'Hello, How I help you?',
                message: "",
                showPopup: true,
                showOnIE: false,
                position: 'right',
                headerTitle: 'Welcome to our Website ',
                headerColor: 'green',
                backgroundColor: 'green',
                size: '60px'
            });
        });
    </script>

	
	
</body>

<!-- Mirrored from herballuckydraw.com/Prize.php by  Website Copier/3.x [XR&CO'2014], Fri, 01 Aug 2025 09:08:12 GMT -->
</html>