const express = require('express');
const router = express.Router();
const Admin = require('../models/Admin');
const Winner = require('../models/Winner');

// Middleware to check if user is authenticated
const requireAuth = (req, res, next) => {
  if (req.session && req.session.adminId) {
    return next();
  } else {
    res.redirect('/admin/login');
  }
};

// Middleware to check if user is already authenticated
const redirectIfAuth = (req, res, next) => {
  if (req.session && req.session.adminId) {
    return res.redirect('/admin/dashboard');
  }
  next();
};

// GET /admin/login - Show login page
router.get('/login', redirectIfAuth, (req, res) => {
  res.render('admin/login', { 
    title: 'Admin Login',
    error: req.flash('error'),
    success: req.flash('success')
  });
});

// POST /admin/login - Handle login
router.post('/login', redirectIfAuth, async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      req.flash('error', 'Username and password are required');
      return res.redirect('/admin/login');
    }
    
    const admin = await Admin.findOne({ username, isActive: true });
    
    if (!admin || !(await admin.comparePassword(password))) {
      req.flash('error', 'Invalid username or password');
      return res.redirect('/admin/login');
    }
    
    // Update last login
    await admin.updateLastLogin();
    
    // Set session
    req.session.adminId = admin._id;
    req.session.adminUsername = admin.username;
    req.session.adminRole = admin.role;
    
    req.flash('success', 'Login successful');
    res.redirect('/admin/dashboard');
    
  } catch (error) {
    console.error('Login error:', error);
    req.flash('error', 'An error occurred during login');
    res.redirect('/admin/login');
  }
});

// GET /admin/logout - Handle logout
router.get('/logout', (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      console.error('Logout error:', err);
    }
    res.redirect('/admin/login');
  });
});

// GET /admin/dashboard - Show dashboard
router.get('/dashboard', requireAuth, async (req, res) => {
  try {
    const stats = {
      totalWinners: await Winner.countDocuments({ isActive: true }),
      pendingWinners: await Winner.countDocuments({ isActive: true, status: 'Pending' }),
      approvedWinners: await Winner.countDocuments({ isActive: true, status: 'Approved' }),
      paidWinners: await Winner.countDocuments({ isActive: true, status: 'Paid' })
    };
    
    const recentWinners = await Winner.find({ isActive: true })
      .sort({ createdAt: -1 })
      .limit(5);
    
    res.render('admin/dashboard', {
      title: 'Admin Dashboard',
      stats,
      recentWinners,
      adminUsername: req.session.adminUsername,
      success: req.flash('success'),
      error: req.flash('error')
    });
  } catch (error) {
    console.error('Dashboard error:', error);
    req.flash('error', 'Error loading dashboard');
    res.redirect('/admin/login');
  }
});

// GET /admin/winners - Show winners management page
router.get('/winners', requireAuth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = 20;
    const search = req.query.search || '';
    const status = req.query.status || '';
    
    const skip = (page - 1) * limit;
    
    // Build search query
    let query = { isActive: true };
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } },
        { wcode: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      query.status = status;
    }
    
    const winners = await Winner.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);
    
    const total = await Winner.countDocuments(query);
    const totalPages = Math.ceil(total / limit);
    
    res.render('admin/winners', {
      title: 'Winners Management',
      winners,
      pagination: {
        page,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
        nextPage: page + 1,
        prevPage: page - 1
      },
      search,
      status,
      adminUsername: req.session.adminUsername,
      success: req.flash('success'),
      error: req.flash('error')
    });
  } catch (error) {
    console.error('Winners page error:', error);
    req.flash('error', 'Error loading winners');
    res.redirect('/admin/dashboard');
  }
});

// GET /admin/upload - Show upload page
router.get('/upload', requireAuth, (req, res) => {
  res.render('admin/upload', {
    title: 'Upload Excel File',
    adminUsername: req.session.adminUsername,
    success: req.flash('success'),
    error: req.flash('error')
  });
});

// GET /admin/settings - Show settings page
router.get('/settings', requireAuth, async (req, res) => {
  try {
    const admin = await Admin.findById(req.session.adminId);
    
    res.render('admin/settings', {
      title: 'Settings',
      admin,
      adminUsername: req.session.adminUsername,
      success: req.flash('success'),
      error: req.flash('error')
    });
  } catch (error) {
    console.error('Settings page error:', error);
    req.flash('error', 'Error loading settings');
    res.redirect('/admin/dashboard');
  }
});

// POST /admin/settings - Update admin settings
router.post('/settings', requireAuth, async (req, res) => {
  try {
    const { currentPassword, newPassword, email } = req.body;
    const admin = await Admin.findById(req.session.adminId);
    
    if (!admin) {
      req.flash('error', 'Admin not found');
      return res.redirect('/admin/settings');
    }
    
    // If changing password, verify current password
    if (newPassword) {
      if (!currentPassword) {
        req.flash('error', 'Current password is required to change password');
        return res.redirect('/admin/settings');
      }
      
      if (!(await admin.comparePassword(currentPassword))) {
        req.flash('error', 'Current password is incorrect');
        return res.redirect('/admin/settings');
      }
      
      admin.password = newPassword;
    }
    
    // Update email if provided
    if (email) {
      admin.email = email;
    }
    
    await admin.save();
    
    req.flash('success', 'Settings updated successfully');
    res.redirect('/admin/settings');
    
  } catch (error) {
    console.error('Settings update error:', error);
    req.flash('error', 'Error updating settings');
    res.redirect('/admin/settings');
  }
});

// GET /admin/bank-details - Show bank details page
router.get('/bank-details', requireAuth, async (req, res) => {
  try {
    // Mock data for now - replace with actual database query
    const bankDetails = [
      {
        winnerName: 'Umesh Bagave',
        phone: '**********',
        bankName: 'State Bank of India',
        accountNumber: '**********',
        ifscCode: 'SBIN0001234',
        prizeAmount: 669,
        status: 'Verified'
      }
    ];

    res.render('admin/bank-details', {
      title: 'Bank Details',
      adminUsername: req.session.adminUsername,
      bankDetails,
      success: req.flash('success'),
      error: req.flash('error')
    });
  } catch (error) {
    console.error('Bank details error:', error);
    req.flash('error', 'Error loading bank details');
    res.redirect('/admin/dashboard');
  }
});

// GET /admin/bank-update - Show bank update page
router.get('/bank-update', requireAuth, (req, res) => {
  res.render('admin/bank-update', {
    title: 'Bank Update',
    adminUsername: req.session.adminUsername,
    success: req.flash('success'),
    error: req.flash('error')
  });
});

// POST /admin/bank-update - Handle bank update
router.post('/bank-update', requireAuth, async (req, res) => {
  try {
    const { phone, winnerName, bankName, accountNumber, ifscCode, accountHolderName, prizeAmount, status, notes } = req.body;

    // Here you would update the database with bank details
    // For now, just flash success message

    req.flash('success', 'Bank details updated successfully');
    res.redirect('/admin/bank-details');
  } catch (error) {
    console.error('Bank update error:', error);
    req.flash('error', 'Error updating bank details');
    res.redirect('/admin/bank-update');
  }
});

// GET /admin/update-prize - Show update prize page
router.get('/update-prize', requireAuth, (req, res) => {
  res.render('admin/update-prize', {
    title: 'Update Prize',
    adminUsername: req.session.adminUsername,
    success: req.flash('success'),
    error: req.flash('error')
  });
});

// POST /admin/update-prize - Handle prize update
router.post('/update-prize', requireAuth, async (req, res) => {
  try {
    const { phone, newPrizeAmount, prizeCategory, prizeStatus, updateDate, prizeDescription, updateReason } = req.body;

    // Here you would update the winner's prize in the database
    // For now, just flash success message

    req.flash('success', 'Prize updated successfully');
    res.redirect('/admin/winners');
  } catch (error) {
    console.error('Prize update error:', error);
    req.flash('error', 'Error updating prize');
    res.redirect('/admin/update-prize');
  }
});

// GET /admin/prize-request - Show prize requests page
router.get('/prize-request', requireAuth, async (req, res) => {
  try {
    // Mock data for now - replace with actual database query
    const prizeRequests = [
      {
        id: 1001,
        winnerName: 'Mukesh Kumar Yadav',
        phone: '9398475946',
        prizeAmount: 325,
        requestDate: new Date(),
        status: 'Pending'
      },
      {
        id: 1002,
        winnerName: 'D Pavan',
        phone: '9392865232',
        prizeAmount: 324,
        requestDate: new Date(),
        status: 'Approved'
      }
    ];

    res.render('admin/prize-request', {
      title: 'Prize Request',
      adminUsername: req.session.adminUsername,
      prizeRequests,
      success: req.flash('success'),
      error: req.flash('error')
    });
  } catch (error) {
    console.error('Prize request error:', error);
    req.flash('error', 'Error loading prize requests');
    res.redirect('/admin/dashboard');
  }
});

// POST /admin/prize-request/update - Update prize request status
router.post('/prize-request/update', requireAuth, async (req, res) => {
  try {
    const { requestId, status } = req.body;

    // Here you would update the prize request status in the database
    // For now, just return success

    res.json({ success: true });
  } catch (error) {
    console.error('Prize request update error:', error);
    res.json({ success: false, message: error.message });
  }
});

// GET /admin/car-processing - Show car processing requests page
router.get('/car-processing', requireAuth, async (req, res) => {
  try {
    // Mock data for now - replace with actual database query
    const carRequests = [
      {
        id: 2001,
        winnerName: 'Mohan Krishna',
        phone: '9391446450',
        carModel: 'Maruti Swift',
        prizeValue: 500000,
        requestDate: new Date(),
        status: 'New'
      },
      {
        id: 2002,
        winnerName: 'Baby Gaming',
        phone: '9387181414',
        carModel: 'Hyundai i20',
        prizeValue: 600000,
        requestDate: new Date(),
        status: 'Processing'
      }
    ];

    res.render('admin/car-processing', {
      title: 'Car Processing Request',
      adminUsername: req.session.adminUsername,
      carRequests,
      success: req.flash('success'),
      error: req.flash('error')
    });
  } catch (error) {
    console.error('Car processing error:', error);
    req.flash('error', 'Error loading car processing requests');
    res.redirect('/admin/dashboard');
  }
});

// POST /admin/car-processing/update - Update car processing status
router.post('/car-processing/update', requireAuth, async (req, res) => {
  try {
    const { requestId, status } = req.body;

    // Here you would update the car processing status in the database
    // For now, just return success

    res.json({ success: true });
  } catch (error) {
    console.error('Car processing update error:', error);
    res.json({ success: false, message: error.message });
  }
});

// Default redirect to dashboard
router.get('/', requireAuth, (req, res) => {
  res.redirect('/admin/dashboard');
});

module.exports = router;
