<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Excel File - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f5f5f5;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background: #1e4d72;
            min-height: 100vh;
            color: white;
            width: 250px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.9);
            padding: 12px 20px;
            border: none;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0;
        }
        .sidebar .nav-link i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            margin-left: 250px;
            padding: 0;
        }
        .top-header {
            background: #1e4d72;
            color: white;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .content-area {
            padding: 30px;
            background: #f5f5f5;
            min-height: calc(100vh - 70px);
        }
        .upload-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
            margin-top: 20px;
        }
        .upload-icon {
            font-size: 48px;
            color: #ffc107;
            margin-bottom: 20px;
        }
        .file-input-wrapper {
            position: relative;
            display: inline-block;
            cursor: pointer;
            width: 100%;
        }
        .file-input {
            position: absolute;
            left: -9999px;
        }
        .file-input-button {
            display: inline-block;
            padding: 10px 20px;
            background: #6c757d;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .file-input-button:hover {
            background: #5a6268;
        }
        .upload-button {
            background: #007bff;
            border: none;
            padding: 12px 30px;
            border-radius: 4px;
            color: white;
            font-weight: 500;
            margin-top: 20px;
        }
        .upload-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="p-3 border-bottom">
            <div class="d-flex align-items-center">
                <button class="btn btn-link text-white p-0 me-3" style="font-size: 18px;">
                    <i class="fas fa-bars"></i>
                </button>
                <h5 class="mb-0">Admin Panel</h5>
            </div>
        </div>
        
        <nav class="nav flex-column mt-3">
            <a class="nav-link" href="/admin/dashboard">
                <i class="fas fa-tachometer-alt"></i>Dashboard
            </a>
            <a class="nav-link active" href="/admin/upload">
                <i class="fas fa-upload"></i>Upload Excel
            </a>
            <a class="nav-link" href="/admin/winners">
                <i class="fas fa-trophy"></i>Winner List
            </a>
            <a class="nav-link" href="/admin/bank-details">
                <i class="fas fa-university"></i>Bank Details
            </a>
            <a class="nav-link" href="/admin/bank-update">
                <i class="fas fa-edit"></i>Bank Update
            </a>
            <a class="nav-link" href="/admin/update-prize">
                <i class="fas fa-gift"></i>Update Prize
            </a>
            <a class="nav-link" href="/admin/prize-request">
                <i class="fas fa-hand-paper"></i>Prize Request
            </a>
            <a class="nav-link" href="/admin/car-processing">
                <i class="fas fa-car"></i>Car Processing Request
            </a>
            <a class="nav-link" href="/admin/logout">
                <i class="fas fa-sign-out-alt"></i>Logout
            </a>
        </nav>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Header -->
        <div class="top-header">
            <h4 class="mb-0">Admin Dashboard</h4>
            <span>Welcome, <%= adminUsername %></span>
        </div>
        
        <!-- Content Area -->
        <div class="content-area">
            <div class="upload-card">
                <div class="text-center">
                    <i class="fas fa-file-excel upload-icon"></i>
                    <h3 class="mb-3">Upload Excel File</h3>
                    
                    <% if (success && success.length > 0) { %>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><%= success %>
                        </div>
                    <% } %>
                    
                    <% if (error && error.length > 0) { %>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i><%= error %>
                        </div>
                    <% } %>
                    
                    <form action="/admin/upload" method="POST" enctype="multipart/form-data" class="mt-4">
                        <div class="mb-3">
                            <label class="form-label text-start d-block">Select Excel File (.xlsx / .xls)</label>
                            <div class="file-input-wrapper">
                                <input type="file" name="excelFile" class="file-input" id="excelFile" accept=".xlsx,.xls" required>
                                <label for="excelFile" class="file-input-button">
                                    Choose File
                                </label>
                                <span class="ms-3" id="fileName">No file chosen</span>
                            </div>
                        </div>
                        
                        <button type="submit" class="upload-button">
                            <i class="fas fa-upload me-2"></i>Upload & Import
                        </button>
                    </form>
                    
                    <div class="mt-4">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Excel Format Requirements:</h6>
                            <p class="mb-2">Your Excel file should have the following columns (exact names):</p>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li><strong>Phone No</strong> - Phone number</li>
                                        <li><strong>Name</strong> - Winner name</li>
                                        <li><strong>Address</strong> - Full address</li>
                                        <li><strong>Paid</strong> - Yes/No/Pending</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="mb-0">
                                        <li><strong>Product</strong> - Product name</li>
                                        <li><strong>Prize Amount:</strong> - Amount</li>
                                        <li><strong>Date:</strong> - Date (YYYY-MM-DD)</li>
                                        <li><strong>Status</strong> - Pending/Approved/Paid/Rejected</li>
                                    </ul>
                                </div>
                            </div>
                            <small class="text-muted mt-2 d-block">
                                <i class="fas fa-download me-1"></i>
                                <a href="/admin/download-template" class="text-decoration-none">Download Excel Template</a>
                            </small>
                        </div>

                        <div class="text-muted">
                            <small>
                                <i class="fas fa-info-circle me-1"></i>
                                Supported formats: .xlsx, .xls | Maximum file size: 10MB
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // File input handler
        document.getElementById('excelFile').addEventListener('change', function(e) {
            const fileName = e.target.files[0] ? e.target.files[0].name : 'No file chosen';
            document.getElementById('fileName').textContent = fileName;
        });
    </script>
</body>
</html>
